#!/usr/bin/env python3
"""
Test script to validate the fixed cohort retention logic.
"""

import os
import sys
from datetime import datetime, timedel<PERSON>

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dags.dependencies.mixpanel.mixpanel_service import MixpanelService

def test_cohort_retention_fix():
    """Test the fixed cohort retention calculation."""
    
    print("🧪 Testing Fixed Cohort Retention Logic")
    print("=" * 60)
    
    try:
        # Initialize MixpanelService
        credentials = {
            "project_id": os.getenv("MIXPANEL_PROJECT_ID"),
            "service_account_username": os.getenv("MIXPANEL_SERVICE_USERNAME"),
            "service_account_secret": os.getenv("MIXPANEL_SERVICE_SECRET"),
            "workspace_id": os.getenv("MIXPANEL_WORKSPACE_ID"),
        }
        
        service = MixpanelService(
            project_id=credentials["project_id"],
            service_account_username=credentials["service_account_username"],
            service_account_secret=credentials["service_account_secret"],
            workspace_id=credentials["workspace_id"],
            respect_rate_limits=False,
        )
        
        print("✅ MixpanelService initialized")
        
        # Test cohort retention with fixed logic
        cohort_start = "2025-04-24"
        cohort_end = "2025-04-30"
        analysis_weeks = 5  # Test with fewer weeks first
        
        print(f"📅 Testing cohort: {cohort_start} to {cohort_end}")
        print(f"📊 Analysis weeks: {analysis_weeks}")
        
        retention_metrics = service.get_weekly_retention_metrics(
            cohort_start, cohort_end, analysis_weeks=analysis_weeks
        )
        
        print(f"\n📈 Results:")
        print(f"   Cohort Size: {retention_metrics.initial_cohort_size:,}")
        print(f"   Cohort Period: {retention_metrics.cohort_start_date} to {retention_metrics.cohort_end_date}")
        
        print(f"\n📊 Weekly Retention Rates:")
        print("-" * 80)
        print(f"{'Week':<6} {'Period':<20} {'Active Users':<15} {'Retention Rate':<15} {'Expected':<15}")
        print("-" * 80)
        
        for week_key, week_data in retention_metrics.weekly_retention_rates.items():
            week_num = int(week_key.split("_")[1]) + 1
            period = f"{week_data['week_start']} to {week_data['week_end']}"
            active_users = week_data['heartbeat_users']
            retention_rate = week_data['heartbeat_retention_rate']
            
            # Expected behavior: retention should decrease over time
            if week_num == 1:
                expected = "100% (baseline)"
            else:
                expected = "< previous week"
            
            print(f"{week_num:<6} {period:<20} {active_users:<15,} {retention_rate:<15.2f}% {expected:<15}")
        
        # Validate results
        print(f"\n🔍 Validation:")
        
        if retention_metrics.initial_cohort_size > 0:
            print("✅ Cohort size > 0")
        else:
            print("❌ Cohort size is 0")
        
        # Check if retention rates are reasonable (should generally decrease)
        rates = [data['heartbeat_retention_rate'] for data in retention_metrics.weekly_retention_rates.values()]
        
        if len(rates) > 1:
            decreasing_trend = all(rates[i] >= rates[i+1] for i in range(len(rates)-1))
            if decreasing_trend:
                print("✅ Retention rates show decreasing trend (expected)")
            else:
                print("⚠️  Retention rates don't show consistent decreasing trend")
        
        # Check for impossible values (>100%)
        max_rate = max(rates) if rates else 0
        if max_rate <= 100:
            print("✅ All retention rates ≤ 100%")
        else:
            print(f"❌ Found retention rate > 100%: {max_rate}%")
        
        print(f"\n🎉 Test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_cohort_retention_fix()
