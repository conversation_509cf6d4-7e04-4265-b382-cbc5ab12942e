#!/usr/bin/env python3
"""
Test script to validate dynamic cohort logic calculations.
"""

from datetime import datetime, <PERSON><PERSON><PERSON>

def test_dynamic_cohort_logic():
    """Test the dynamic cohort calculation logic."""
    
    print("🧪 Testing Dynamic Cohort Logic")
    print("=" * 50)
    
    # Base cohort period (fixed)
    base_cohort_start = "2024-06-19"
    base_cohort_end = "2024-06-25"
    base_cohort_dt = datetime.strptime(base_cohort_start, "%Y-%m-%d")
    
    print(f"📅 Base Cohort Period: {base_cohort_start} to {base_cohort_end}")
    print(f"📅 Base Cohort Date Object: {base_cohort_dt}")
    
    # Test different execution dates
    test_dates = [
        datetime(2024, 6, 19),  # Same as cohort start
        datetime(2024, 6, 25),  # Same as cohort end
        datetime(2024, 7, 2),   # 1 week after cohort
        datetime(2024, 7, 9),   # 2 weeks after cohort
        datetime(2024, 8, 6),   # 4 weeks after cohort
        datetime(2024, 12, 25), # 26 weeks after cohort
        datetime(2025, 7, 2),   # Current date (54 weeks after)
    ]
    
    print("\n🔍 Testing Different Execution Dates:")
    print("-" * 70)
    print(f"{'Execution Date':<15} {'Days Since':<12} {'Weeks Since':<12} {'Analysis Weeks':<15}")
    print("-" * 70)
    
    for execution_date in test_dates:
        # Calculate weeks since cohort
        days_since = (execution_date - base_cohort_dt).days
        weeks_since_cohort = days_since // 7
        
        # Calculate analysis weeks (same logic as implementation)
        analysis_weeks = max(1, min(weeks_since_cohort + 1, 12))
        
        print(f"{execution_date.strftime('%Y-%m-%d'):<15} {days_since:<12} {weeks_since_cohort:<12} {analysis_weeks:<15}")
    
    print("\n📊 Expected Behavior:")
    print("- Week 0: June 19-25, 2024 (Base Cohort)")
    print("- Week 1: June 26 - July 2, 2024")
    print("- Week 2: July 3 - July 9, 2024")
    print("- ...")
    print("- Analysis weeks capped at 12 for performance")
    
    print("\n✅ Dynamic cohort logic validated!")

if __name__ == "__main__":
    test_dynamic_cohort_logic()
